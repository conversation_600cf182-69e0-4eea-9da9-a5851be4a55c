@extends($activeTemplate . 'layouts.master')
@section('content')

 <style>
            .home-wrap .tool[data-v-56b6ece7] {
                background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAA2CAYAAACMRWrdAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAAMSSURBVGiB7ZpNaxNRFIafc2YynZRGa62timJREelCN4IgiN3qRhcuBBf6FwT3/Qm6cusfcOFOBEGX4sZFwW2lflQtta1JM2mazHExiaQ1sYmZOs3FZ5PhDoT3zbn3ZIbzCj1ii0xRZwbhOiJHMZlENACNQMrJpxchWkY0IvaiGD9S9cqIHxEHEeQivKCM5CMII2SkjBQidDza1MkoF04Xo/q5Uj5/KgKqQElE4l50SteGPnIX5Q7mn0aG38XeiefqH1vAv/iRfbOLvf5Anak0NIW2/QawJiKVbr5lR2P2mRsYD8D38c8+ZGLuSa9SU6YGLO9k8I/G7DMPMP/mHjG0nSKw2mmLartFW2TKPvEWGTtG4d6tPWgKoABMmpnf7uZvFbNFpoh5iXf8KZMLD3ddXv/UgK8iUmtd3GLM5hkl4CXe8VcDYqpJVUS2NLCtW3GIWWTftwEzBRCY2Vjrwq/9aR+YwfybjD+69u91pULBzCoiUobWiimPCa/cJ7j9IzNp/XOgeaHQqBbDXxh78TozSengm9kINCvmMYt/ci+29L9hBEBsnlGG9D1H6uezVpQin5Qh7iCjz7JWkjKhEus03sRc1kpSZlhRPUNw6U3WSlLGU5BxZKqYtZKU8dVMc4QXXDOmKqLrBFfXs1aSNpq8zruHgu+qMXXVmLMV89w0FrtqTP3AuVYPoLHloqxF7AaqGrpZMQjcPGNI3lVjhf/GBglFJtw0tqmH3TSWC8862u6Dy27+QcNEPWsRu4ECPQ2tB4RYSQZnrlFTwMWtuKmAi+2+qiT5CdcoaWMovZG1khSpiEjcnGiWMpWSLmvQGPyJSAk3umOtmdhpTQ2sZCQmTZabF7+MNabtgzycKLbmq7bkPETkO0mMbtCoAqutC+2yVEsM1nmrAUvbw2Jt02+N4NUhIPgHwvqhSmLqt0J0jPWZmQKjJCmzvUixcXTasnMQ0ywEDtIST8qYGrDSjBZ1ovvobGJwPxD2Kexv2SDJBnf1MNG1sSaNLTpCcv5yJJVsG+jsg5ikMnWSh/RKu3P0J34C3NvjqHVCAlkAAAAASUVORK5CYII=) no-repeat center;
            }
            .text-\#ff0000{
                color:#ff5722;
            }
        </style>
    </head>

    <body class="">
        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="position: absolute; width: 0; height: 0;">
            <symbol id="svg-5-activity" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M27.7199 34.1934C26.7197 34.1928 25.7433 33.8876 24.9209 33.3183L19.4874 29.5721C19.2727 29.4261 19.0191 29.3481 18.7595 29.3481C18.4998 29.3481 18.2462 29.4261 18.0315 29.5721L12.598 33.3183C11.8567 33.8292 10.9897 34.1276 10.091 34.1813C9.19225 34.2349 8.29597 34.0416 7.49916 33.6224C6.70235 33.2033 6.03537 32.5741 5.57042 31.8031C5.10547 31.0321 4.86026 30.1487 4.86133 29.2483V7.84938C4.8628 6.5501 5.37959 5.30445 6.29832 4.38572C7.21705 3.46699 8.4627 2.9502 9.76198 2.94873H27.7495C29.0488 2.9502 30.2945 3.46699 31.2132 4.38572C32.1319 5.30445 32.6487 6.5501 32.6502 7.84938V29.2539C32.6487 30.5631 32.1281 31.8184 31.2025 32.7444C30.2768 33.6703 29.0218 34.1914 27.7125 34.1934H27.7199ZM18.7585 27.1301C19.4688 27.131 20.1622 27.347 20.7473 27.7498L26.1807 31.4961C26.5884 31.7765 27.065 31.9403 27.559 31.9696C28.0529 31.9989 28.5455 31.8927 28.9836 31.6625C29.4216 31.4322 29.7884 31.0867 30.0443 30.6632C30.3002 30.2397 30.4356 29.7543 30.4357 29.2594V7.84938C30.4347 7.13777 30.1512 6.45566 29.6475 5.953C29.1438 5.45033 28.4611 5.16824 27.7495 5.16873H9.76198C9.05133 5.16971 8.37006 5.45245 7.86756 5.95496C7.36505 6.45746 7.08231 7.13873 7.08133 7.84938V29.2539C7.08149 29.7487 7.21684 30.2341 7.47278 30.6576C7.72871 31.0812 8.09549 31.4267 8.53351 31.6569C8.97152 31.8872 9.46412 31.9934 9.95809 31.964C10.4521 31.9347 10.9286 31.771 11.3363 31.4905L16.7716 27.7498C17.3562 27.3474 18.0489 27.1313 18.7585 27.1301Z"
                    fill="white"
                ></path>
                <path
                    d="M21.9265 22.8977C21.6138 22.8977 21.3058 22.8215 21.0292 22.6757L18.7593 21.4824L16.4912 22.6757C16.1727 22.8431 15.8138 22.9179 15.4549 22.8918C15.0961 22.8656 14.7518 22.7395 14.461 22.5276C14.1702 22.3158 13.9446 22.0268 13.8097 21.6932C13.6748 21.3597 13.636 20.9951 13.6977 20.6407L14.1306 18.1136L12.2954 16.3246C12.0384 16.0737 11.8567 15.756 11.7708 15.4073C11.6848 15.0585 11.6982 14.6927 11.8092 14.3512C11.9202 14.0096 12.1245 13.7059 12.3991 13.4744C12.6736 13.2428 13.0074 13.0927 13.3628 13.0409L15.8992 12.6709L17.0332 10.3787C17.1923 10.0565 17.4383 9.78514 17.7435 9.59543C18.0487 9.40572 18.4009 9.30518 18.7602 9.30518C19.1196 9.30518 19.4717 9.40572 19.7769 9.59543C20.0821 9.78514 20.3281 10.0565 20.4872 10.3787L21.6212 12.6764L24.1576 13.0464C24.513 13.0982 24.8468 13.2484 25.1213 13.4799C25.3959 13.7115 25.6002 14.0152 25.7112 14.3567C25.8223 14.6983 25.8356 15.0641 25.7496 15.4128C25.6637 15.7615 25.482 16.0793 25.225 16.3302L23.3898 18.1191L23.8227 20.6462C23.8695 20.9224 23.8555 21.2054 23.7818 21.4757C23.7081 21.7459 23.5765 21.9969 23.3961 22.2111C23.2156 22.4254 22.9907 22.5977 22.7369 22.7163C22.4831 22.8349 22.2066 22.8967 21.9265 22.8977ZM14.273 15.1573L15.7919 16.6373C16.0157 16.8549 16.1832 17.1238 16.2799 17.4206C16.3765 17.7175 16.3995 18.0334 16.3469 18.3411L15.988 20.4316L17.8657 19.4456C18.142 19.2997 18.4497 19.2235 18.7621 19.2235C19.0744 19.2235 19.3821 19.2997 19.6584 19.4456L21.5361 20.4316L21.1772 18.3411C21.1246 18.0334 21.1476 17.7175 21.2443 17.4206C21.3409 17.1238 21.5084 16.8549 21.7322 16.6373L23.2511 15.1573L21.1513 14.852C20.8427 14.8068 20.5497 14.6874 20.2974 14.5041C20.0451 14.3207 19.8411 14.0789 19.7028 13.7994L18.7593 11.8957L17.8213 13.7975C17.6837 14.0777 17.4798 14.3201 17.2274 14.5039C16.975 14.6876 16.6817 14.8071 16.3728 14.852L14.273 15.1573Z"
                    fill="#06ca4f"
                ></path>
            </symbol>
            <symbol id="svg-5-combine" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_716_14304)">
                    <path
                        d="M12.9555 12.4633V7.99933C12.9554 7.56171 13.0415 7.12836 13.2089 6.72403C13.3763 6.31969 13.6218 5.9523 13.9312 5.64282C14.2406 5.33335 14.6079 5.08785 15.0122 4.92036C15.4165 4.75287 15.8499 4.66667 16.2875 4.66667H18.9555V3.328C18.9555 2.44536 19.3061 1.59887 19.9302 0.974749C20.5544 0.350628 21.4008 0 22.2835 0L28.2941 0C29.1768 0 30.0233 0.350628 30.6474 0.974749C31.2715 1.59887 31.6221 2.44536 31.6221 3.328V9.33867C31.6221 10.2213 31.2715 11.0678 30.6474 11.6919C30.0233 12.316 29.1768 12.6667 28.2941 12.6667H22.2835C21.4008 12.6667 20.5544 12.316 19.9302 11.6919C19.3061 11.0678 18.9555 10.2213 18.9555 9.33867V7.33333H16.2875C16.2001 7.33333 16.1135 7.35056 16.0327 7.38404C15.952 7.41752 15.8786 7.46659 15.8168 7.52844C15.755 7.59029 15.706 7.66372 15.6726 7.74452C15.6392 7.82532 15.6221 7.9119 15.6221 7.99933V24C15.6221 24.3707 15.9181 24.666 16.2881 24.666H18.9555V22.6607C18.9557 21.7781 19.3064 20.9318 19.9305 20.3078C20.5546 19.6839 21.401 19.3333 22.2835 19.3333H28.2941C29.1768 19.3333 30.0233 19.684 30.6474 20.3081C31.2715 20.9322 31.6221 21.7787 31.6221 22.6613V28.672C31.6221 29.5546 31.2715 30.4011 30.6474 31.0253C30.0233 31.6494 29.1768 32 28.2941 32H22.2835C21.4008 32 20.5544 31.6494 19.9302 31.0253C19.3061 30.4011 18.9555 29.5546 18.9555 28.672V27.3333H16.2881C15.8504 27.3338 15.4168 27.2479 15.0123 27.0805C14.6077 26.9132 14.2402 26.6677 13.9306 26.3582C13.6211 26.0486 13.3756 25.6811 13.2083 25.2765C13.0409 24.872 12.955 24.4384 12.9555 24.0007V20.29L10.2655 22.98C9.95644 23.2891 9.58956 23.5342 9.18577 23.7015C8.78199 23.8687 8.34921 23.9548 7.91215 23.9548C7.47509 23.9548 7.04231 23.8687 6.63852 23.7015C6.23474 23.5342 5.86785 23.2891 5.55882 22.98L1.30881 18.73C0.999757 18.421 0.754598 18.0541 0.587335 17.6503C0.420073 17.2465 0.333984 16.8137 0.333984 16.3767C0.333984 15.9396 0.420073 15.5068 0.587335 15.103C0.754598 14.6993 0.999757 14.3324 1.30881 14.0233L5.55882 9.77333C5.86785 9.46428 6.23474 9.21912 6.63852 9.05185C7.04231 8.88459 7.47509 8.7985 7.91215 8.7985C8.34921 8.7985 8.78199 8.88459 9.18577 9.05185C9.58956 9.21912 9.95644 9.46428 10.2655 9.77333L12.9555 12.4633ZM22.2835 29.3333H28.2941C28.4695 29.3333 28.6378 29.2637 28.7618 29.1396C28.8858 29.0156 28.9555 28.8474 28.9555 28.672V22.6613C28.9555 22.4859 28.8858 22.3177 28.7618 22.1937C28.6378 22.0697 28.4695 22 28.2941 22H22.2835C22.1081 22 21.9399 22.0697 21.8158 22.1937C21.6918 22.3177 21.6221 22.4859 21.6221 22.6613V28.672C21.6221 29.0373 21.9181 29.3333 22.2835 29.3333ZM22.2835 10H28.2941C28.4695 10 28.6378 9.93032 28.7618 9.8063C28.8858 9.68228 28.9555 9.51406 28.9555 9.33867V3.328C28.9555 3.1526 28.8858 2.98439 28.7618 2.86037C28.6378 2.73634 28.4695 2.66667 28.2941 2.66667H22.2835C22.1081 2.66667 21.9399 2.73634 21.8158 2.86037C21.6918 2.98439 21.6221 3.1526 21.6221 3.328V9.33867C21.6221 9.704 21.9181 10 22.2835 10ZM8.38081 21.0947L12.6308 16.844C12.7547 16.72 12.8242 16.5519 12.8242 16.3767C12.8242 16.2014 12.7547 16.0333 12.6308 15.9093L8.38081 11.6587C8.3194 11.5972 8.24649 11.5485 8.16625 11.5153C8.08601 11.482 8 11.4649 7.91315 11.4649C7.82629 11.4649 7.74029 11.482 7.66005 11.5153C7.5798 11.5485 7.50689 11.5972 7.44548 11.6587L3.19415 15.9093C3.07031 16.0333 3.00075 16.2014 3.00075 16.3767C3.00075 16.5519 3.07031 16.72 3.19415 16.844L7.44481 21.0947C7.50623 21.1561 7.57914 21.2048 7.65938 21.2381C7.73962 21.2713 7.82563 21.2884 7.91248 21.2884C7.99934 21.2884 8.08534 21.2713 8.16558 21.2381C8.24583 21.2048 8.3194 21.1561 8.38081 21.0947Z"
                        fill="white"
                    ></path>
                    <path d="M20 6H15C14.4477 6 14 6.44772 14 7V25C14 25.5523 14.4477 26 15 26H19.4305" stroke="#06ca4f" stroke-width="3.5" stroke-linecap="round"></path>
                </g>
                <defs>
                    <clippath id="clip0_716_14304">
                        <rect width="32" height="32" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="svg-5-download" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M24.7644 22.6667C24.3492 22.6667 23.951 22.4996 23.6574 22.2023C23.3638 21.905 23.1989 21.5017 23.1989 21.0812C23.1989 20.6607 23.3638 20.2574 23.6574 19.9601C23.951 19.6628 24.3492 19.4957 24.7644 19.4957C25.3328 19.4957 25.8951 19.3766 26.4158 19.1459C26.9365 18.9151 27.4045 18.5778 27.7903 18.1551C28.176 17.7323 28.4713 17.2334 28.6574 16.6895C28.8436 16.1456 28.9166 15.5686 28.8719 14.9948C28.8273 14.4209 28.6658 13.8627 28.3978 13.3551C28.1298 12.8475 27.761 12.4015 27.3146 12.0452C26.8681 11.6889 26.3537 11.4301 25.8037 11.2848C25.2537 11.1396 24.68 11.1112 24.1187 11.2014C23.8936 11.2373 23.6636 11.223 23.4446 11.1595C23.2256 11.0961 23.0229 10.985 22.8506 10.8341C22.6784 10.6831 22.5407 10.4959 22.4473 10.2855C22.3539 10.0751 22.3069 9.84658 22.3097 9.61591V9.55883C22.2912 7.87731 21.6186 6.27102 20.438 5.08857C19.2573 3.90613 17.6638 3.24294 16.0033 3.24294C14.3428 3.24294 12.7493 3.90613 11.5687 5.08857C10.388 6.27102 9.71543 7.87731 9.69693 9.55883V9.61194C9.69859 9.84123 9.65112 10.0682 9.55779 10.2771C9.46447 10.486 9.32751 10.6719 9.15636 10.822C8.98522 10.9721 8.78396 11.0829 8.56646 11.1466C8.34897 11.2103 8.12041 11.2255 7.89656 11.1911C7.33569 11.1046 6.76322 11.1363 6.21502 11.2843C5.66682 11.4322 5.1547 11.6933 4.71074 12.051C4.26678 12.4087 3.90055 12.8554 3.63499 13.3632C3.36943 13.8709 3.21026 14.4287 3.16746 15.0016C3.12466 15.5746 3.19914 16.1503 3.38625 16.6927C3.57335 17.2351 3.86905 17.7325 4.25481 18.1538C4.64057 18.5751 5.10808 18.9112 5.62806 19.141C6.14804 19.3709 6.70927 19.4895 7.27661 19.4894C7.69182 19.4894 8.09002 19.6564 8.38361 19.9538C8.67721 20.2511 8.84215 20.6544 8.84215 21.0749C8.84215 21.4954 8.67721 21.8986 8.38361 22.196C8.09002 22.4933 7.69182 22.6603 7.27661 22.6603C5.39998 22.6669 3.59392 21.9364 2.23805 20.6224C0.882174 19.3085 0.0820921 17.5133 0.00597332 15.6144C-0.0701455 13.7154 0.583628 11.8605 1.82987 10.4396C3.07612 9.0186 4.81777 8.14228 6.68875 7.99477C7.05453 5.76127 8.19124 3.73176 9.89644 2.26768C11.6016 0.803598 13.7646 0 16.0002 0C18.2357 0 20.3987 0.803598 22.1039 2.26768C23.8091 3.73176 24.9458 5.76127 25.3116 7.99477C27.1791 8.14209 28.9178 9.01552 30.1636 10.4321C31.4094 11.8487 32.0656 13.6985 31.9948 15.5941C31.924 17.4898 31.1316 19.2841 29.7837 20.6013C28.4357 21.9185 26.6368 22.6563 24.7637 22.6603L24.7644 22.6667Z"
                    fill="white"
                ></path>
                <path
                    d="M10.9286 27.6371L15.3629 31.7553C15.5322 31.912 15.7615 32 16.0005 32C16.2395 32 16.4688 31.912 16.6381 31.7553L21.0724 27.6371C21.1978 27.5197 21.2831 27.3705 21.3176 27.2083C21.352 27.0461 21.3341 26.878 21.266 26.7252C21.1979 26.5724 21.0827 26.4417 20.9349 26.3495C20.7871 26.2574 20.6132 26.2078 20.4352 26.2071C20.3165 26.2071 20.1991 26.1854 20.0895 26.1433C19.9799 26.1011 19.8803 26.0393 19.7964 25.9614C19.7126 25.8835 19.646 25.791 19.6006 25.6893C19.5552 25.5875 19.5319 25.4784 19.5319 25.3682V16.8389C19.5319 16.6164 19.4367 16.403 19.2673 16.2457C19.0979 16.0884 18.8682 16 18.6286 16H13.3691C13.1296 16 12.8998 16.0884 12.7304 16.2457C12.561 16.403 12.4658 16.6164 12.4658 16.8389V25.3697C12.4658 25.4799 12.4425 25.589 12.3971 25.6908C12.3517 25.7926 12.2852 25.885 12.2013 25.9629C12.1174 26.0408 12.0178 26.1026 11.9082 26.1448C11.7986 26.1869 11.6812 26.2086 11.5626 26.2086C11.3851 26.2102 11.2121 26.2603 11.0652 26.3526C10.9182 26.445 10.8037 26.5755 10.7361 26.7278C10.6685 26.8802 10.6507 27.0477 10.6849 27.2094C10.7192 27.3711 10.8039 27.5198 10.9286 27.6371Z"
                    fill="#06ca4f"
                ></path>
            </symbol>
            <symbol id="svg-5-help" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M21.1056 34.7898C20.9665 34.7898 20.8316 34.7622 20.7046 34.7077L18.7793 33.882C16.4184 32.8695 13.9149 32.356 11.3386 32.356H7.96078C6.27545 32.356 4.9043 31.0181 4.9043 29.3735V11.5928C4.9043 9.94828 6.27541 8.61035 7.96078 8.61035H11.3386C13.6351 8.61035 15.8666 9.06796 17.9711 9.97043L19.8962 10.7961C20.2782 10.9604 20.6898 11.0448 21.1056 11.0443C21.5214 11.0448 21.933 10.9604 22.315 10.796L24.2401 9.97043C26.3446 9.06796 28.5761 8.61035 30.8725 8.61035H34.2504C35.9356 8.61035 37.3066 9.94828 37.3066 11.5928V29.3735C37.3066 31.0181 35.9356 32.3561 34.2504 32.3561H30.8725C28.2962 32.3561 25.7928 32.8695 23.4318 33.8821L21.5066 34.7078C21.3799 34.7622 21.2435 34.7901 21.1056 34.7898Z"
                    fill="#413D4B"
                ></path>
                <path
                    d="M34.2504 9.63574C35.3703 9.63574 36.2813 10.5137 36.2813 11.5928V29.3735C36.2813 30.4527 35.3703 31.3307 34.2504 31.3307H30.8725C28.1566 31.3307 25.5172 31.872 23.0276 32.9397L21.1057 33.764L19.1836 32.9397C16.6942 31.872 14.0547 31.3307 11.3387 31.3307H7.96086C6.84093 31.3307 5.92977 30.4527 5.92977 29.3735V11.5928C5.92977 10.5137 6.84093 9.6357 7.96086 9.6357H11.3387C13.4955 9.6357 15.591 10.0654 17.567 10.9128L19.492 11.7384C20.0044 11.9582 20.5473 12.0696 21.1057 12.0696C21.664 12.0696 22.2069 11.9582 22.7192 11.7384L24.6443 10.9128C26.6204 10.0654 28.7159 9.63574 30.8726 9.63574H34.2504ZM34.2504 7.58496H30.8725C28.4509 7.58496 26.0553 8.07633 23.836 9.02805L21.9109 9.8537C21.6564 9.96276 21.3825 10.019 21.1056 10.0189C20.8288 10.019 20.5549 9.96276 20.3004 9.8537L18.3752 9.02805C16.1559 8.07633 13.7603 7.58496 11.3386 7.58496H7.96078C5.70652 7.58496 3.87891 9.37943 3.87891 11.5928V29.3735C3.87891 31.5871 5.70652 33.3814 7.96078 33.3814H11.3386C13.7603 33.3814 16.1559 33.8727 18.3752 34.8245L20.3004 35.6501C20.5549 35.7592 20.8288 35.8154 21.1056 35.8153C21.3825 35.8154 21.6564 35.7592 21.9109 35.6501L23.836 34.8245C26.0553 33.8727 28.4509 33.3814 30.8725 33.3814H34.2504C36.5047 33.3814 38.3321 31.5871 38.3321 29.3735V11.5928C38.3321 9.37939 36.5047 7.58496 34.2504 7.58496Z"
                    fill="white"
                ></path>
                <path
                    d="M16.929 14.1008C14.6184 13.1577 12.3398 13.0005 10.0667 12.9742C9.31344 12.9664 8.70329 13.5956 8.70312 14.3665V26.2449C8.70325 27.0173 9.31344 27.6408 10.0667 27.6458C12.3404 27.663 14.6216 27.7658 16.9482 28.3824C17.7203 28.599 18.3242 28.0522 18.3232 27.1168C18.3232 26.5847 18.3005 16.0926 18.3005 15.6986C18.3018 15.1789 17.6955 14.432 16.929 14.1008ZM32.143 12.9742C29.8647 12.9999 27.5811 13.154 25.2653 14.0787C24.4971 14.4035 23.8894 15.1357 23.8906 15.6454C23.8906 16.0317 23.8679 16.6724 23.8679 17.194C23.8669 18.1112 24.4721 18.6475 25.2459 18.435C27.5779 17.8304 28.5103 17.4386 32.1429 17.0237C32.8931 16.9386 33.5095 16.4075 33.5095 15.6501V14.3393C33.5095 13.5834 32.8978 12.9666 32.143 12.9742ZM32.143 19.1758C29.8647 19.2015 27.5811 19.3555 25.2653 20.2804C24.4971 20.605 23.8894 21.3374 23.8906 21.847C23.8906 22.2334 23.8679 22.5079 23.8679 22.7065C23.8669 23.6238 24.4721 24.1599 25.2459 23.9475C27.5779 23.3429 28.5103 22.9512 32.1429 22.5362C32.8931 22.4511 33.5095 22.0446 33.5095 21.1627V20.5488C33.5095 19.7929 32.8978 19.168 32.143 19.1758Z"
                    fill="#06ca4f"
                ></path>
            </symbol>
            <symbol id="svg-5-invite" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M30.9295 15.0461C30.624 14.8754 30.2787 14.789 29.9289 14.7956C29.5791 14.8023 29.2374 14.9017 28.9387 15.0839L18.0001 21.7708L9.45725 16.5473V7.49145C9.45725 6.81285 10.0081 6.26205 10.6867 6.26205H25.6519C26.3305 6.26205 26.8813 6.81285 26.8813 7.49145V12.0941C26.8813 12.6899 27.3655 13.174 27.9613 13.174C28.5571 13.174 29.0413 12.6899 29.0413 12.0941V7.49145C29.0413 5.62305 27.5203 4.10205 25.6519 4.10205H10.6867C8.81825 4.10205 7.29725 5.62305 7.29725 7.49145V15.2279L7.06325 15.0839C6.76419 14.9017 6.42215 14.8023 6.07206 14.7956C5.72198 14.789 5.3764 14.8754 5.07065 15.0461C4.76535 15.2174 4.51121 15.4669 4.33436 15.7691C4.15751 16.0712 4.06435 16.415 4.06445 16.7651V28.3426C4.06445 30.8393 6.09485 32.8696 8.59145 32.8696H27.4105C29.9071 32.8696 31.9375 30.8393 31.9375 28.3426V16.7651C31.9375 16.0541 31.5505 15.3953 30.9295 15.0461ZM29.7775 28.3444C29.7775 29.6495 28.7155 30.7115 27.4105 30.7115H8.58965C7.28465 30.7115 6.22265 29.6495 6.22265 28.3444V17.1035L17.4367 23.9578C17.6064 24.0612 17.8013 24.1158 18.0001 24.1158C18.1988 24.1158 18.3937 24.0612 18.5635 23.9578L29.7775 17.1035V28.3444Z"
                    fill="white"
                ></path>
                <path
                    d="M18.6527 10.9261H12.8027C12.2069 10.9261 11.7227 10.4419 11.7227 9.84611C11.7227 9.25031 12.2069 8.76611 12.8027 8.76611H18.6545C19.2503 8.76611 19.7345 9.25031 19.7345 9.84611C19.7345 10.4419 19.2503 10.9261 18.6527 10.9261ZM23.5955 15.3811H12.8027C12.2069 15.3811 11.7227 14.8969 11.7227 14.3011C11.7227 13.7053 12.2069 13.2211 12.8027 13.2211H23.5955C24.1913 13.2211 24.6755 13.7053 24.6755 14.3011C24.6755 14.8969 24.1913 15.3811 23.5955 15.3811Z"
                    fill="#06ca4f"
                ></path>
            </symbol>
            <symbol id="svg-5-laba" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 14 16">
                <g>
                    <path
                        d="M13.2991,14C13.2991,14,0.700856,14,0.700856,14C0.436373,14,0.194098,13.8577,0.0749797,13.6327C-0.0441387,13.4077,-0.0199111,13.1385,0.137567,12.9365C0.137567,12.9365,1.70428,10.9192,1.70428,10.9192C1.70428,10.9192,1.70428,7,1.70428,7C1.70428,4.24231,4.08059,2,7,2C9.91941,2,12.2957,4.24231,12.2957,7C12.2957,7,12.2957,10.9192,12.2957,10.9192C12.2957,10.9192,13.8624,12.9365,13.8624,12.9365C14.0199,13.1385,14.0441,13.4077,13.925,13.6327C13.8059,13.8577,13.5636,14,13.2991,14ZM2.08788,12.6673C2.08788,12.6673,11.9101,12.6673,11.9101,12.6673C11.9101,12.6673,11.0319,11.5365,11.0319,11.5365C10.943,11.4212,10.8946,11.2827,10.8946,11.1404C10.8946,11.1404,10.8946,7,10.8946,7C10.8946,4.97885,9.14615,3.33269,6.99798,3.33269C4.84981,3.33269,3.10341,4.97885,3.10341,7C3.10341,7,3.10341,11.1385,3.10341,11.1385C3.10341,11.2808,3.05496,11.4192,2.96613,11.5346C2.96613,11.5346,2.08788,12.6673,2.08788,12.6673ZM8.75043,16C8.75043,16,5.24956,16,5.24956,16C4.86394,16,4.54899,15.7019,4.54899,15.3327C4.54899,14.9635,4.86192,14.6654,5.24956,14.6654C5.24956,14.6654,8.74841,14.6654,8.74841,14.6654C9.13404,14.6654,9.44899,14.9635,9.44899,15.3327C9.44899,15.7019,9.13605,16,8.75043,16ZM7.34928,1.33269C7.34928,1.33269,6.6487,1.33269,6.6487,1.33269C6.26308,1.33269,5.94812,1.03462,5.94812,0.665385C5.94812,0.296154,6.26308,0,6.65072,0C6.65072,0,7.3513,0,7.3513,0C7.73692,0,8.05188,0.298077,8.05188,0.667308C8.05188,1.03654,7.73692,1.33269,7.34928,1.33269Z"
                        fill="#FFFFFF"
                        fill-opacity="1"
                    ></path>
                </g>
            </symbol>
            <symbol id="svg-5-lang" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M16 7.99352C16 3.57882 12.4183 0 8.00008 0C3.58176 0 0 3.57882 0 7.99352C0 12.0716 3.05622 15.4362 7.00487 15.9258C7.22747 15.9727 7.49859 16 7.82151 16C7.91216 16 7.99958 15.9952 8.08401 15.9866C12.4636 15.9416 16 12.3804 16 7.99352ZM14.8561 8.10662C14.8331 7.70778 14.7804 7.31738 14.7002 6.93797C14.7073 6.8025 14.7092 6.67415 14.7056 6.55482C14.8048 7.01873 14.8571 7.50002 14.8571 7.99352C14.8571 8.0313 14.8567 8.06898 14.8561 8.10662ZM1.14288 7.99352C1.14288 7.50901 1.19333 7.0363 1.28906 6.58022C1.58518 6.89691 2.28314 6.88943 2.45336 6.41626C2.75792 6.59776 3.1672 6.63081 3.1672 6.99359C3.1672 8.19088 3.20983 9.47448 4.29776 9.49431C4.32839 9.49471 4.90447 9.71262 5.17864 10.4236C5.27344 10.6694 5.6484 10.4236 6.0596 10.4236C6.26488 10.4236 6.0596 10.7694 6.0596 11.5172C6.0596 12.2621 7.66569 13.4091 7.66569 13.4091C7.65824 13.9022 7.67849 14.3009 7.71953 14.6194C7.357 14.6128 7.05148 14.6608 6.81145 14.7425C3.5909 14.1801 1.14288 11.3726 1.14288 7.99352ZM9.68819 14.6359C9.65264 14.4619 9.49712 14.3665 9.21337 14.4411C9.43978 13.477 9.54984 12.937 10.0225 12.5269C10.7063 11.9342 10.1039 11.275 9.58353 11.3527C9.17337 11.4146 9.43257 10.8449 9.06649 10.8133C8.70041 10.7827 8.22232 10.0546 7.69546 9.80405C7.41617 9.67143 7.1417 9.31601 6.71097 9.30009C6.32921 9.28528 5.77129 9.62287 5.77129 9.36265C5.77129 8.52445 5.68641 7.92633 5.66897 7.68747C5.65488 7.49557 5.54352 7.62283 6.0596 7.63524C6.34047 7.64276 6.20328 7.07111 6.48129 7.04878C6.75433 7.02718 7.40497 7.30437 7.57073 7.19389C7.72473 7.09102 8.70273 9.76094 8.70273 7.63522C8.70273 7.383 8.5721 6.94447 8.70273 6.70559C9.21937 5.76164 9.70304 4.99233 9.65961 4.87979C9.63496 4.81643 9.13104 4.76411 8.72784 4.89939C8.59177 4.94483 8.77112 5.15794 8.57568 5.20346C7.84344 5.37249 7.19648 5.00603 7.42304 4.66157C7.65503 4.30856 8.49559 4.50758 8.56928 3.79939C8.61168 3.39374 8.64681 2.92392 8.67032 2.57474C9.65581 2.72885 9.54733 1.29585 8.08199 1.14246C11.0465 1.17714 13.5585 3.09153 14.4804 5.74843C14.4338 5.7059 14.3795 5.68006 14.3169 5.67375C13.8738 4.56718 12.7985 5.36801 13.1633 6.34403C11.2087 7.84643 11.709 8.89427 12.3512 9.49431C12.6891 9.80974 13.0113 10.2841 13.221 10.6248C12.9927 11.2905 14.0622 11.0239 14.5897 9.89424C13.9188 12.221 12.0471 14.0392 9.68819 14.6359Z"
                    fill="white"
                ></path>
            </symbol>
            <symbol id="svg-5-message" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M3.5 8.112H1.87775L0.77 9.30893C0.711848 9.37197 0.634245 9.41697 0.547653 9.43785C0.46106 9.45874 0.369643 9.45451 0.285725 9.42573C0.201807 9.39695 0.129423 9.34501 0.0783334 9.27692C0.0272435 9.20882 -9.61651e-05 9.12783 2.54156e-07 9.04488V0.8112C2.54156e-07 0.596057 0.0921873 0.389725 0.256282 0.237595C0.420376 0.0854656 0.642936 5.04243e-09 0.875 0H9.625C9.85707 0 10.0796 0.0854656 10.2437 0.237595C10.4078 0.389725 10.5 0.596057 10.5 0.8112V2.4336H4.375C4.14294 2.4336 3.92038 2.51907 3.75628 2.6712C3.59219 2.82333 3.5 3.02966 3.5 3.2448V8.112ZM14 10.5456V11.5945C14 11.6747 13.9743 11.7531 13.9262 11.8198C13.8782 11.8865 13.8098 11.9384 13.7299 11.9691C13.65 11.9998 13.562 12.0079 13.4772 11.9922C13.3923 11.9766 13.3144 11.938 13.2532 11.8812L11.8125 10.5456H4.8125C4.75505 10.5456 4.69816 10.5351 4.64508 10.5147C4.592 10.4943 4.54377 10.4645 4.50314 10.4268C4.46252 10.3891 4.43029 10.3444 4.4083 10.2952C4.38632 10.246 4.375 10.1933 4.375 10.14V3.6504C4.375 3.54283 4.42109 3.43966 4.50314 3.3636C4.58519 3.28753 4.69647 3.2448 4.8125 3.2448H13.5625C13.6785 3.2448 13.7898 3.28753 13.8719 3.3636C13.9539 3.43966 14 3.54283 14 3.6504V10.5456ZM7.4375 7.7064C7.55242 7.70642 7.66621 7.68545 7.77239 7.64469C7.87856 7.60393 7.97504 7.54418 8.05631 7.46885C8.13757 7.39352 8.20204 7.30409 8.24602 7.20566C8.29 7.10724 8.31263 7.00174 8.31263 6.8952C8.31263 6.78866 8.29 6.68317 8.24602 6.58474C8.20204 6.48631 8.13757 6.39688 8.05631 6.32155C7.97504 6.24622 7.87856 6.18647 7.77239 6.14572C7.66621 6.10496 7.55242 6.08399 7.4375 6.084C7.20546 6.08404 6.98293 6.16952 6.81887 6.32164C6.6548 6.47377 6.56263 6.68008 6.56263 6.8952C6.56263 7.11033 6.6548 7.31664 6.81887 7.46876C6.98293 7.62089 7.20546 7.70637 7.4375 7.7064ZM10.9375 7.7064C11.0524 7.70642 11.1662 7.68545 11.2724 7.64469C11.3786 7.60393 11.475 7.54418 11.5563 7.46885C11.6376 7.39352 11.702 7.30409 11.746 7.20566C11.79 7.10724 11.8126 7.00174 11.8126 6.8952C11.8126 6.78866 11.79 6.68317 11.746 6.58474C11.702 6.48631 11.6376 6.39688 11.5563 6.32155C11.475 6.24622 11.3786 6.18647 11.2724 6.14572C11.1662 6.10496 11.0524 6.08399 10.9375 6.084C10.7055 6.08404 10.4829 6.16952 10.3189 6.32164C10.1548 6.47377 10.0626 6.68008 10.0626 6.8952C10.0626 7.11033 10.1548 7.31664 10.3189 7.46876C10.4829 7.62089 10.7055 7.70637 10.9375 7.7064Z"
                    fill="white"
                ></path>
            </symbol>
            <symbol id="svg-5-quan-1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 24 24">
                <defs>
                    <filter id="master_svg0_0_26294" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                    <filter id="master_svg1_0_26295" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                    <filter id="master_svg2_0_26299" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                    <filter id="master_svg3_0_26300" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                    <filter id="master_svg4_0_26301" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                    <filter id="master_svg5_0_26302" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                    <filter id="master_svg6_0_26303" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                    <filter id="master_svg7_0_26304" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="4" height="4">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                </defs>
                <g>
                    <g>
                        <rect x="3" y="3" width="18" height="3" rx="1.5" fill="#FFF500" fill-opacity="1"></rect>
                    </g>
                    <g>
                        <rect x="8" y="8" width="8" height="8" rx="3" fill="#FF8A00" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg0_0_26294)">
                        <rect x="6" y="0" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg1_0_26295)">
                        <rect x="14" y="0" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g>
                        <rect x="3" y="18" width="18" height="3" rx="1.5" fill="#FFF500" fill-opacity="1"></rect>
                    </g>
                    <g transform="matrix(0,1,-1,0,9,-3)">
                        <rect x="6" y="3" width="18" height="2.9999992847442627" rx="1.4999996423721313" fill="#FFF500" fill-opacity="1"></rect>
                    </g>
                    <g transform="matrix(0,1,-1,0,24,-18)">
                        <rect x="21" y="3" width="18" height="2.9999992847442627" rx="1.4999996423721313" fill="#FFF500" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg2_0_26299)">
                        <rect x="6" y="20" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg3_0_26300)">
                        <rect x="0" y="14" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg4_0_26301)">
                        <rect x="20" y="14" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg5_0_26302)">
                        <rect x="0" y="6" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg6_0_26303)">
                        <rect x="20" y="6" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg7_0_26304)">
                        <rect x="14" y="20" width="4" height="4" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                </g>
            </symbol>
            <symbol id="svg-5-quan-2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 24 24">
                <defs>
                    <filter id="master_svg0_0_26309" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="24" height="22">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur in="BackgroundImage" stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="SourceAlpha" operator="in" result="effect1_foregroundBlur"></fecomposite>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_foregroundBlur" result="shape"></feblend>
                    </filter>
                </defs>
                <g>
                    <g>
                        <rect x="0.185546875" y="13" width="6" height="11" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g>
                        <rect x="8.185546875" y="6" width="6" height="18" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g>
                        <rect x="16.185546875" y="0" width="6" height="24" rx="1" fill="#FFF000" fill-opacity="1"></rect>
                    </g>
                    <g filter="url(#master_svg0_0_26309)">
                        <path
                            d="M23.5111,3.3854C21.9508,6.04233,17.1492,14.2187,16.4139,15.4709C16.3506,15.5786,16.2752,15.6669,16.177,15.7441C15.2142,16.500799999999998,9.64607,20.8769,8.47826,21.7947C8.33698,21.9058,8.17255,21.9754,7.99438,21.9988C6.95409,22.135,3.16312,22.6316,1.34134,22.8702C0.789269,22.9426,0.298243,22.5601,0.232293,22.0072C0.195582,21.6995,0.155136,21.3604,0.118424,21.0526C0.0535401,20.5087,0.439049,20.0023,0.9822,19.9312C2.64812,19.713,5.94044,19.2817,6.89957,19.1561C7.07774,19.1328,7.24244,19.0622,7.38372,18.9512C8.45154,18.1119,13.1492,14.4199,14.0447,13.7161C14.143,13.6389,14.2258,13.5432,14.2891,13.4355C15.004,12.2181,19.4892,4.5806000000000004,21.054,1.9159000000000002C21.342,1.425538,21.979,1.249641,22.4569,1.557829C22.6948,1.711256,22.952,1.8770829999999998,23.1869,2.02855C23.6374,2.31906,23.7826,2.92317,23.5111,3.3854"
                            fill-rule="evenodd"
                            fill="#FF8A00"
                            fill-opacity="1"
                        ></path>
                    </g>
                </g>
            </symbol>
            <symbol id="svg-5-quan-3" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 24 28">
                <g>
                    <g>
                        <path
                            d="M6.78405625,20.12103980255127C6.78405625,20.12103980255127,6.78405625,18.64059980255127,6.78405625,18.64059980255127C6.78405625,14.02558480255127,3.80406625,13.08903980255127,3.80406625,13.08903980255127C3.80406625,13.08903980255127,0.84765625,14.09756980255127,0.84765625,18.64067980255127C0.84765625,18.64067980255127,0.84765625,20.12111980255127,0.84765625,20.12111980255127C0.84765625,20.12111980255127,6.78405625,20.12111980255127,6.78405625,20.12111980255127C6.78405625,20.12111980255127,6.78405625,20.12103980255127,6.78405625,20.12103980255127Z"
                            fill="#FFF000"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M6.78445,20.946083676757812C6.78445,20.946083676757812,0.848057,20.946083676757812,0.848057,20.946083676757812C0.379675,20.946083676757812,0,20.576383676757814,0,20.120503676757814C0,20.120503676757814,0,18.640063676757812,0,18.640063676757812C0,13.569813676757812,3.3801,12.358436076757812,3.52401,12.309313776757813C3.69878,12.249737676757812,3.88876,12.247446276757813,4.06499,12.302790776757812C4.2106,12.348610776757813,7.63242,13.483293676757812,7.63242,18.640063676757812C7.63242,18.640063676757812,7.63242,20.120503676757814,7.63242,20.120503676757814C7.63251,20.576473676757814,7.25283,20.946083676757812,6.78445,20.946083676757812ZM1.69611,19.29491367675781C1.69611,19.29491367675781,5.9364,19.29491367675781,5.9364,19.29491367675781C5.9364,19.29491367675781,5.9364,18.640063676757812,5.9364,18.640063676757812C5.9364,15.491033676757812,4.44475,14.343303676757813,3.81702,13.999943676757812C3.18318,14.363533676757813,1.69611,15.546263676757812,1.69611,18.640143676757813C1.69611,18.640143676757813,1.69611,19.29491367675781,1.69611,19.29491367675781Z"
                            fill="#FFF000"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M23.15124375,20.12103980255127C23.15124375,20.12103980255127,23.15124375,18.64059980255127,23.15124375,18.64059980255127C23.15124375,14.02558480255127,20.171253749999998,13.08903980255127,20.171253749999998,13.08903980255127C20.171253749999998,13.08903980255127,17.21484375,14.09756980255127,17.21484375,18.64067980255127C17.21484375,18.64067980255127,17.21484375,20.12111980255127,17.21484375,20.12111980255127C17.21484375,20.12111980255127,23.15124375,20.12111980255127,23.15124375,20.12111980255127C23.15124375,20.12111980255127,23.15124375,20.12103980255127,23.15124375,20.12103980255127Z"
                            fill="#FFF000"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M23.1516375,20.946053676757813C23.1516375,20.946053676757813,17.2152445,20.946053676757813,17.2152445,20.946053676757813C16.7469475,20.946053676757813,16.3671875,20.57635367675781,16.3671875,20.12047367675781C16.3671875,20.12047367675781,16.3671875,18.640033676757813,16.3671875,18.640033676757813C16.3671875,13.569783676757812,19.7472875,12.358404276757813,19.8912875,12.309281976757813C20.0660575,12.249748176757812,20.256027500000002,12.247457076757813,20.4322575,12.302759076757813C20.5779575,12.348579076757812,23.9996975,13.483263676757812,23.9996975,18.640033676757813C23.9996975,18.640033676757813,23.9996975,20.12047367675781,23.9996975,20.12047367675781C23.9996975,20.576443676757812,23.6199375,20.946053676757813,23.1516375,20.946053676757813ZM18.0632975,19.294883676757813C18.0632975,19.294883676757813,22.3035875,19.294883676757813,22.3035875,19.294883676757813C22.3035875,19.294883676757813,22.3035875,18.640033676757813,22.3035875,18.640033676757813C22.3035875,15.491003676757813,20.8119375,14.343273676757812,20.1842075,13.999913676757812C19.5503675,14.363503676757812,18.0632975,15.546233676757812,18.0632975,18.640113676757814C18.0632975,18.640113676757814,18.0632975,19.294883676757813,18.0632975,19.294883676757813Z"
                            fill="#FFF000"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M19.63781875,18.964895133686067C19.63781875,18.964895133686067,19.63781875,15.145995133686066,19.63781875,15.145995133686066C19.63781875,3.2411051336860655,11.95053875,0.8251951336860657,11.95053875,0.8251951336860657C11.95053875,0.8251951336860657,4.32421875,3.426695133686066,4.32421875,15.146095133686066C4.324727584,17.471295133686066,4.32421875,18.964995133686067,4.32421875,18.964995133686067C4.32421875,18.964995133686067,19.63781875,18.964895133686067,19.63781875,18.964895133686067Z"
                            fill="#FFF000"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M4.3246195,19.79099999999818C4.2132235,19.79099999999818,4.1029145,19.76969999999818,3.9999975,19.728199999998182C3.8970795,19.68669999999818,3.8035705,19.625899999998182,3.7248134999999998,19.54919999999818C3.6460565,19.47249999999818,3.5835945,19.38139999999818,3.5409989,19.28119999999818C3.4984029,19.180999999998182,3.4765068946,19.07359999999818,3.476562606045,18.96519999999818C3.476562606045,18.96499999999818,3.47707144,17.47149999999818,3.476562606045,15.14669999999818C3.476562606045,8.835759999998182,5.6647225,5.083309999998181,7.5003325,3.046759999998181C9.5304125,0.794398999998181,11.5840725,0.075975299998181,11.6704825,0.04641939999818101C11.8452525,-0.01315670000181899,12.0352325,-0.01544680000181899,12.2114625,0.03989769999818101C12.2988125,0.06738969999818101,14.3754625,0.738259999998181,16.426762500000002,2.971299999998181C18.2787625,4.987299999998181,20.4862625,8.736109999998181,20.4862625,15.14639999999818C20.4862625,15.14639999999818,20.4862625,18.96539999999818,20.4862625,18.96539999999818C20.4862625,19.42119999999818,20.1065625,19.79089999999818,19.6382625,19.79089999999818C19.6382625,19.79089999999818,4.3246195,19.79099999999818,4.3246195,19.79099999999818ZM11.9612825,1.718479999998181C11.3991925,1.975889999998181,10.0448425,2.701499999998181,8.7077125,4.211329999998181C6.3950625,6.822739999998181,5.1726725,10.604099999998182,5.1726725,15.14649999999818C5.1729325,16.43909999999818,5.1729325,17.47479999999818,5.1728425,18.139899999998182C5.1728425,18.139899999998182,18.7901625,18.139799999998182,18.7901625,18.139799999998182C18.7901625,18.139799999998182,18.7901625,15.14639999999818,18.7901625,15.14639999999818C18.7901625,10.53289999999818,17.5598625,6.729939999998181,15.2321625,4.1485899999981815C13.8889625,2.658979999998181,12.5284625,1.963669999998181,11.9612825,1.718479999998181Z"
                            fill="#FFF000"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M11.357063125,27.7581904876709C11.357063125,27.7581904876709,9.558078125,26.006880487670898,9.558078125,26.006880487670898C8.233244125,24.7171504876709,8.233244125,22.626350487670898,9.558078125,21.336625487670897C10.882823125,20.0469784876709,13.030613125,20.0469784876709,14.355363125,21.336625487670897C15.680113125,22.626190487670897,15.680203125,24.7171504876709,14.355363125,26.006880487670898C14.355363125,26.006880487670898,12.556383125,27.7581904876709,12.556383125,27.7581904876709C12.225213125,28.0805804876709,11.688223125,28.0805804876709,11.357063125,27.7581904876709Z"
                            fill="#FF8A00"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M3.50125625,25.23909442504883C3.50125625,25.23909442504883,2.55677425,24.31964442504883,2.55677425,24.31964442504883C1.86128325,23.642584425048828,1.86128325,22.544804425048827,2.55677425,21.86773942504883C3.2521762499999998,21.190759425048828,4.37984625,21.190759425048828,5.075336249999999,21.86773942504883C5.77082625,22.544804425048827,5.7709062499999995,23.642584425048828,5.075336249999999,24.31964442504883C5.075336249999999,24.31964442504883,4.1309362499999995,25.23909442504883,4.1309362499999995,25.23909442504883C4.08959625,25.279344425048826,4.04050625,25.31128442504883,3.98648625,25.33306442504883C3.93246625,25.354854425048828,3.87456625,25.366064425048826,3.81609625,25.366064425048826C3.75762625,25.366064425048826,3.6997262500000003,25.354854425048828,3.64570625,25.33306442504883C3.59168625,25.31128442504883,3.54259625,25.279344425048826,3.50125625,25.23909442504883Z"
                            fill="#FF8A00"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M3.81524625,25.35980442504883C3.75474625,25.35998442504883,3.69480625,25.348474425048828,3.63890625,25.325944425048828C3.58300625,25.303414425048828,3.53225625,25.270304425048828,3.48959625,25.228534425048828C3.48959625,25.228534425048828,2.55588425,24.319564425048828,2.55588425,24.319564425048828C1.86158025,23.64366442504883,1.86158025,22.54373442504883,2.55588425,21.867745425048827C2.72089025,21.706258425048826,2.91715825,21.578234425048826,3.13332625,21.491082425048827C3.34949625,21.40392972504883,3.58127625,21.35937926104883,3.81524625,21.360011051748828C4.291006250000001,21.360011051748828,4.7381062499999995,21.540235425048827,5.07461625,21.867745425048827C5.76891625,22.54373442504883,5.76891625,23.64366442504883,5.07461625,24.319654425048828C5.07461625,24.319654425048828,4.1409062500000005,25.228624425048828,4.1409062500000005,25.228624425048828C4.098236249999999,25.27037442504883,4.04747625,25.30346442504883,3.99157625,25.32598442504883C3.93567625,25.348494425048827,3.8757462499999997,25.35999442504883,3.81524625,25.35980442504883ZM3.81524625,21.442569525048828C3.59242625,21.44198262504883,3.37170625,21.484415425048827,3.16584625,21.56741542504883C2.95998225,21.65041442504883,2.7730722500000002,21.772333425048828,2.61592725,21.926114425048826C1.95461275,22.56990442504883,1.95461275,23.61749442504883,2.61592725,24.261284425048828C2.61592725,24.261284425048828,3.54963625,25.170254425048828,3.54963625,25.170254425048828C3.6206162500000003,25.239354425048827,3.71492625,25.27732442504883,3.81524625,25.277244425048828C3.91557625,25.277164425048827,4.00987625,25.239274425048826,4.08085625,25.170164425048828C4.08085625,25.170164425048828,5.01456625,24.261194425048828,5.01456625,24.261194425048828C5.67588625,23.61749442504883,5.67588625,22.56990442504883,5.01456625,21.926114425048826C4.85742625,21.772333425048828,4.67051625,21.65041442504883,4.46465625,21.56741542504883C4.25879625,21.484415425048827,4.03806625,21.44198262504883,3.81524625,21.442569525048828Z"
                            fill="#FF8A00"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M19.86837375,25.23909442504883C19.86837375,25.23909442504883,18.92389875,24.31964442504883,18.92389875,24.31964442504883C18.22849175,23.642664425048828,18.22849175,22.544884425048828,18.92389875,21.86773942504883C19.61930375,21.190759425048828,20.74696375,21.190759425048828,21.44245375,21.86773942504883C22.137943749999998,22.544804425048827,22.13786375,23.642584425048828,21.44245375,24.31964442504883C21.44245375,24.31964442504883,20.49797375,25.23909442504883,20.49797375,25.23909442504883C20.45664375,25.279344425048826,20.40756375,25.31127442504883,20.35355375,25.33305442504883C20.29953375,25.354844425048828,20.24164375,25.36605442504883,20.18317375,25.36605442504883C20.12471375,25.36605442504883,20.06682375,25.354844425048828,20.01280375,25.33305442504883C19.958793749999998,25.31127442504883,19.90971375,25.279344425048826,19.86837375,25.23909442504883Z"
                            fill="#FF8A00"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M20.184266875,25.35979442504883C20.123756875,25.35997442504883,20.063826875,25.348464425048828,20.007916875,25.32593442504883C19.952016874999998,25.30340442504883,19.901266875,25.270294425048828,19.858606875,25.228534425048828C19.858606875,25.228534425048828,18.924897875,24.319564425048828,18.924897875,24.319564425048828C18.230762875,23.64365442504883,18.230762875,22.54372442504883,18.924897875,21.867738425048827C19.261405875,21.540229425048828,19.708586875,21.360004425048828,20.184266875,21.360004425048828C20.659936875,21.360004425048828,21.107116875,21.540229425048828,21.443626875,21.867738425048827C22.137926874999998,22.54372442504883,22.137846875,23.64357442504883,21.443626875,24.31964442504883C21.443626875,24.31964442504883,20.509916875000002,25.22861442504883,20.509916875000002,25.22861442504883C20.467246875,25.27036442504883,20.416496875,25.303454425048827,20.360596875,25.325974425048827C20.304696875,25.348494425048827,20.244756875,25.35998442504883,20.184266875,25.35979442504883ZM20.184266875,21.44256292504883C19.961436875,21.441957725048827,19.740706875,21.48438242504883,19.534846875,21.567383425048828C19.328982875,21.650384425048827,19.142073875,21.772311425048827,18.984940875,21.926107425048826C18.323710975,22.56989442504883,18.323710975,23.61748442504883,18.984940875,24.26127442504883C18.984940875,24.26127442504883,19.918646875,25.17024442504883,19.918646875,25.17024442504883C19.989546875,25.239264425048827,20.083856875,25.277234425048828,20.184266875,25.277234425048828C20.284676875,25.277234425048828,20.378976875,25.239264425048827,20.449876875,25.170164425048828C20.449876875,25.170164425048828,21.383586875,24.261194425048828,21.383586875,24.261194425048828C22.044896875,23.61748442504883,22.044816875,22.56989442504883,21.383586875,21.926107425048826C21.226446875,21.772311425048827,21.039536875,21.650384425048827,20.833676875000002,21.567383425048828C20.627816875,21.48438242504883,20.407086875,21.441957725048827,20.184266875,21.44256292504883Z"
                            fill="#FF8A00"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M11.957828750000001,6.581596851348877C12.89459875,6.581596851348877,13.65394875,7.320825851348877,13.65394875,8.232766851348877C13.65394875,8.232766851348877,13.65394875,11.535106851348877,13.65394875,11.535106851348877C13.65394875,12.447046851348876,12.89459875,13.186276851348877,11.957828750000001,13.186276851348877C11.02106875,13.186276851348877,10.26171875,12.447046851348876,10.26171875,11.535106851348877C10.26171875,11.535106851348877,10.26171875,8.232766851348877,10.26171875,8.232766851348877C10.26171875,7.320825851348877,11.02106875,6.581596851348877,11.957828750000001,6.581596851348877Z"
                            fill="#FF8A00"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path
                            d="M11.957828750000001,13.186276851348877C11.02259475,13.186276851348877,10.26171875,12.445556851348876,10.26171875,11.535106851348877C10.26171875,11.535106851348877,10.26171875,8.232766851348877,10.26171875,8.232766851348877C10.26171875,7.322311851348877,11.02259475,6.581596851348877,11.957828750000001,6.581596851348877C12.89306875,6.581596851348877,13.65394875,7.322311851348877,13.65394875,8.232766851348877C13.65394875,8.232766851348877,13.65394875,11.535106851348877,13.65394875,11.535106851348877C13.65394875,12.445556851348876,12.89306875,13.186276851348877,11.957828750000001,13.186276851348877ZM11.957828750000001,6.664155351348877C11.06932275,6.664155351348877,10.34652445,7.367800851348877,10.34652445,8.232766851348877C10.34652445,8.232766851348877,10.34652445,11.535106851348877,10.34652445,11.535106851348877C10.34652445,12.400066851348877,11.06932275,13.103716851348878,11.957828750000001,13.103716851348878C12.846338750000001,13.103716851348878,13.56913875,12.400066851348877,13.56913875,11.535106851348877C13.56913875,11.535106851348877,13.56913875,8.232766851348877,13.56913875,8.232766851348877C13.56913875,7.367800851348877,12.846338750000001,6.664155351348877,11.957828750000001,6.664155351348877Z"
                            fill="#DFECFD"
                            fill-opacity="1"
                        ></path>
                    </g>
                </g>
            </symbol>
            <symbol id="svg-5-quan-4" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 26 26">
                <defs>
                    <clippath id="master_svg0_0_26225">
                        <rect x="0" y="0" width="26" height="26" rx="0"></rect>
                    </clippath>
                </defs>
                <g clip-path="url(#master_svg0_0_26225)">
                    <g>
                        <path
                            d="M24.80399997997284,22.438046472930907C24.80399997997284,22.767346472930907,24.74769997997284,23.075046472930907,24.63499997997284,23.36104647293091C24.52229997997284,23.647046472930906,24.36199997997284,23.898346472930907,24.153999979972838,24.11504647293091C23.94599997997284,24.33164647293091,23.70329997997284,24.500646472930907,23.42599997997284,24.622046472930908C23.14869997997284,24.74334647293091,22.84529997997284,24.804046472930906,22.51599997997284,24.804046472930906C22.51599997997284,24.804046472930906,3.6399999799728393,24.804046472930906,3.6399999799728393,24.804046472930906C3.310669979972839,24.804046472930906,2.9986699799728394,24.74334647293091,2.7039999799728394,24.622046472930908C2.4093299799728394,24.500646472930907,2.149332979972839,24.33164647293091,1.9239999799728393,24.11504647293091C1.6986669799728393,23.898346472930907,1.5209999799728393,23.647046472930906,1.3909999799728394,23.36104647293091C1.2609999799728393,23.075046472930907,1.1959999799728391,22.767346472930907,1.1959999799728394,22.438046472930907C1.1959999799728394,22.438046472930907,1.1959999799728394,9.463996472930909,1.1959999799728394,9.463996472930909C1.1959999799728394,8.805336472930907,1.4256669799728394,8.246336472930908,1.8849999799728394,7.786996472930908C2.3443299799728394,7.3276664729309084,2.903329979972839,7.097996472930908,3.5619999799728395,7.097996472930908C3.5619999799728395,7.097996472930908,22.43799997997284,7.097996472930908,22.43799997997284,7.097996472930908C23.09669997997284,7.097996472930908,23.65569997997284,7.3276664729309084,24.11499997997284,7.786996472930908C24.57429997997284,8.246336472930908,24.80399997997284,8.805336472930907,24.80399997997284,9.463996472930909C24.80399997997284,9.463996472930909,24.80399997997284,13.000046472930908,24.80399997997284,13.000046472930908C24.80399997997284,13.000046472930908,18.90199997997284,13.000046472930908,18.90199997997284,13.000046472930908C18.24329997997284,13.000046472930908,17.684299979972838,13.225346472930909,17.22499997997284,13.676046472930908C16.765699979972837,14.126646472930908,16.53599997997284,14.681346472930908,16.53599997997284,15.340046472930908C16.55329997997284,15.790646472930908,16.64869997997284,16.189346472930907,16.821999979972837,16.536046472930906C16.960699979972837,16.83064647293091,17.190299979972842,17.099346472930907,17.51099997997284,17.34204647293091C17.83169997997284,17.584646472930906,18.29529997997284,17.706046472930907,18.90199997997284,17.706046472930907C18.90199997997284,17.706046472930907,24.80399997997284,17.706046472930907,24.80399997997284,17.706046472930907C24.80399997997284,17.706046472930907,24.80399997997284,22.438046472930907,24.80399997997284,22.438046472930907ZM21.26799997997284,5.901996472930908C21.26799997997284,5.901996472930908,9.46399997997284,5.901996472930908,9.46399997997284,5.901996472930908C10.39999997997284,5.416666472930908,11.283999979972839,4.948666472930908,12.11599997997284,4.497996472930908C12.843999979972839,4.116666472930908,13.56329997997284,3.735336472930908,14.273999979972839,3.353996472930908C14.98469997997284,2.972666472930908,15.539299979972839,2.678000472930908,15.93799997997284,2.4700004729309084C16.54469997997284,2.140666472930908,17.08629997997284,1.9890001729309081,17.56299997997284,2.015000132930908C18.039699979972838,2.041000172930908,18.44269997997284,2.1233334729309083,18.77199997997284,2.2620004729309082C19.15329997997284,2.4526664729309084,19.48269997997284,2.7040004729309084,19.75999997997284,3.015996472930908C19.75999997997284,3.015996472930908,21.26799997997284,5.901996472930908,21.26799997997284,5.901996472930908ZM17.73199997997284,15.340046472930908C17.73199997997284,15.010646472930908,17.84469997997284,14.733346472930908,18.069999979972838,14.508046472930909C18.29529997997284,14.282646472930908,18.57269997997284,14.170046472930908,18.90199997997284,14.170046472930908C19.23129997997284,14.170046472930908,19.50869997997284,14.282646472930908,19.73399997997284,14.508046472930909C19.95929997997284,14.733346472930908,20.07199997997284,15.010646472930908,20.07199997997284,15.340046472930908C20.07199997997284,15.669346472930908,19.95929997997284,15.951046472930908,19.73399997997284,16.185046472930907C19.50869997997284,16.41904647293091,19.23129997997284,16.536046472930906,18.90199997997284,16.536046472930906C18.57269997997284,16.536046472930906,18.29529997997284,16.41904647293091,18.069999979972838,16.185046472930907C17.84469997997284,15.951046472930908,17.73199997997284,15.669346472930908,17.73199997997284,15.340046472930908Z"
                            fill="#FFF000"
                            fill-opacity="1"
                        ></path>
                    </g>
                    <g>
                        <path d="M21.5,6C21.5,6,9,6,9,6C9,6,12.11368,4.03346,14.77588,2.35207C16.77672,1.08838,19.4287,1.85738,20.487000000000002,3.97403C21.0437,5.0874,21.5,6,21.5,6Z" fill="#FF8A00" fill-opacity="1"></path>
                    </g>
                </g>
            </symbol>
            <symbol id="svg-5-quan-5" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 24 24">
                <g>
                    <path
                        d="M19.3548,9.1711C20.2435,9.55677,21.0508,10.1077,21.7339,10.7946C23.1941,12.2462,23.9497,14.0718,24,16.2705C23.9497,18.4529,23.1948,20.2738,21.7339,21.7339C20.2738,23.1941,18.4529,23.9497,16.2705,24C14.4666,23.959,12.9128,23.4426,11.6113,22.4516C11.6113,22.4516,1.58323,22.4516,1.58323,22.4516C0.70298,22.4516,0,21.7254,0,20.8413C0,20.8413,0,1.61034,0,1.61034C0,0.726196,0.70298,0,1.58323,0C1.58323,0,17.7716,0,17.7716,0C18.6519,0,19.3549,0.726196,19.3549,1.61034C19.3549,1.61034,19.3548,9.1711,19.3548,9.1711ZM20.6392,20.6392C21.8137,19.4648,22.4106,18.0248,22.4524,16.3061C22.4106,14.4999,21.8137,13.0575,20.6423,11.8932C19.4756,10.7319,18.0441,10.1295,16.2705,10.0661C14.4805,10.1295,13.0467,10.7319,11.8893,11.8893C10.7318,13.0467,10.1295,14.4805,10.0653,16.2705C10.1295,18.0441,10.7327,19.4756,11.8924,20.6423C13.0498,21.806,14.4813,22.4028,16.2705,22.4508C18.0433,22.4028,19.4725,21.806,20.6392,20.6392ZM3.80439,5.1027C3.47922,5.11819,3.24079,5.25599,3.08671,5.51532C2.93187,5.7747,2.93187,6.03714,3.08671,6.30502C3.24076,6.57134,3.48001,6.70449,3.80439,6.70449C3.80439,6.70449,13.7566,6.70449,13.7566,6.70449C13.9997,6.70449,14.1979,6.63019,14.3528,6.48076C14.4285,6.40787,14.4882,6.31993,14.5279,6.22259C14.5677,6.12525,14.5866,6.02067,14.5835,5.91558C14.5835,5.68799,14.5061,5.49521,14.3528,5.33884C14.1979,5.18166,13.9997,5.1027,13.7566,5.1027C13.7566,5.1027,3.80439,5.1027,3.80439,5.1027ZM3.83305,9.06887C3.58142,9.06887,3.37625,9.14399,3.21602,9.29341C3.13822,9.36512,3.07668,9.45266,3.03555,9.55015C2.99441,9.64763,2.97463,9.7528,2.97754,9.85856C2.97754,10.0862,3.05652,10.2782,3.21599,10.4353C3.37549,10.5925,3.58142,10.6707,3.83302,10.6707C3.83302,10.6707,7.25497,10.6707,7.25497,10.6707C7.50657,10.6707,7.71174,10.5925,7.87123,10.4353C8.03073,10.2782,8.11048,10.0862,8.11048,9.85856C8.11329,9.75273,8.0934,9.64753,8.05213,9.55004C8.01086,9.45255,7.94918,9.36503,7.87123,9.29339C7.71174,9.14396,7.50657,9.06889,7.25497,9.06889C7.25497,9.06889,3.83305,9.06887,3.83305,9.06887Z"
                        fill="#FFF000"
                        fill-opacity="1"
                    ></path>
                </g>
            </symbol>
            <symbol id="svg-5-quan-6" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 24 24">
                <g>
                    <path
                        d="M5.18522,10.3103C5.18522,10.3103,8.74582,16.6543,8.74582,16.6543C9.0867,16.5596,9.43933,16.5141,9.79306,16.5191C9.79306,16.5191,12.6537,5.52204,12.6537,5.52204C12.1412,5.19086,11.7419,4.71114,11.5089,4.14688C11.276,3.58263,11.2206,2.9607,11.35,2.36409C11.4795,1.76748,11.7877,1.22458,12.2335,0.807847C12.6793,0.391117,13.2414,0.120382,13.845,0.031776C14.4485,-0.0568296,15.0647,0.0409097,15.6113,0.311949C16.1579,0.582988,16.6089,1.01443,16.9041,1.5487C17.1993,2.08296,17.3246,2.69463,17.2634,3.30207C17.2022,3.9095,16.9574,4.48381,16.5615,4.94832C16.5615,4.94832,20.1965,10.611,20.1965,10.611C20.8884,10.4198,21.6257,10.4828,22.2751,10.7887C22.9245,11.0945,23.4431,11.623,23.7368,12.2784C24.0306,12.9338,24.0802,13.6728,23.8767,14.3616C23.6731,15.0505,23.2299,15.6436,22.6272,16.0336C22.0245,16.4237,21.3022,16.5849,20.591,16.488C19.8799,16.3912,19.2269,16.0427,18.7502,15.5056C18.2736,14.9686,18.0048,14.2785,17.9926,13.5602C17.9804,12.842,18.2255,12.1431,18.6835,11.5902C18.6835,11.5902,15.0348,5.91095,15.0348,5.91095C14.8229,5.96567,14.6058,5.99712,14.3871,6.00473C14.3871,6.00473,11.532,16.9632,11.532,16.9632C12.2264,17.3434,12.7825,17.9341,13.1206,18.6503C13.4587,19.3666,13.5614,20.1717,13.4139,20.95C13.2665,21.7283,12.8764,22.4398,12.2998,22.9824C11.7231,23.5251,10.9895,23.8709,10.2043,23.9704C9.41906,24.0698,8.62253,23.9177,7.92904,23.5358C7.23555,23.154,6.68076,22.5621,6.34428,21.8451C6.00781,21.128,5.90695,20.3227,6.05619,19.5448C6.20543,18.7668,6.5971,18.0562,7.17497,17.5148C7.17497,17.5148,3.61987,11.1709,3.61987,11.1709C2.91896,11.3195,2.18803,11.212,1.55945,10.8679C0.93087,10.5238,0.446192,9.96574,0.193179,9.29485C-0.0598335,8.62396,-0.0644543,7.88457,0.180154,7.21057C0.424762,6.53657,0.902427,5.97251,1.52666,5.62053C2.15089,5.26855,2.88041,5.15192,3.58313,5.29177C4.28585,5.43161,4.91532,5.81867,5.35754,6.38287C5.79976,6.94708,6.0255,7.65111,5.99389,8.3675C5.96228,9.08389,5.67541,9.76527,5.18522,10.2883C5.18522,10.2883,5.18522,10.3103,5.18522,10.3103Z"
                        fill="#FFF000"
                        fill-opacity="1"
                    ></path>
                </g>
            </symbol>
            <symbol id="svg-5-recharge" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M4.2168 20.9386C4.2168 30.2614 11.6365 37.8452 20.7584 37.8452C29.0764 37.8452 36.1311 31.496 37.177 23.0755C37.2877 22.1485 36.6314 21.3036 35.7045 21.1888C34.7816 21.0698 33.9326 21.7302 33.8178 22.6571C32.9852 29.3878 27.3742 34.4614 20.7625 34.4614C13.5068 34.4614 7.60059 28.3952 7.60059 20.9345C7.60059 13.4737 13.5068 7.40752 20.7625 7.40752C24.9943 7.40752 28.8712 9.46485 31.316 12.8424L29.8557 12.3909C28.9615 12.112 28.0141 12.6124 27.7393 13.5065C27.4645 14.4007 27.9649 15.3481 28.8549 15.6229L34.2197 17.2841C34.3838 17.3333 34.552 17.3579 34.7201 17.3579C34.9949 17.3579 35.2697 17.2923 35.5199 17.1569C35.9219 16.9436 36.2172 16.5786 36.3444 16.1438L37.9645 10.6436C38.227 9.7495 37.7143 8.80615 36.8201 8.54364C35.926 8.28115 34.9826 8.79384 34.7201 9.68798L34.2798 11.1822C31.2264 6.74689 26.2337 4.02783 20.7584 4.02783C11.6365 4.02783 4.2168 11.6157 4.2168 20.9386Z"
                    fill="white"
                ></path>
                <path
                    d="M26.4883 15.3927C26.7277 15.9182 26.3408 16.4795 25.7704 16.5699L23.7203 16.8951C23.2573 16.9685 22.823 16.7006 22.5934 16.2919C22.5241 16.1685 22.4575 16.0694 22.3937 15.9945C22.2402 15.799 21.9818 15.6036 21.6188 15.4081V18.4345C23.7411 19 25.1584 19.5969 25.8705 20.2252C26.813 21.07 27.2842 22.1416 27.2842 23.4402C27.2842 24.2011 27.1132 24.8993 26.7711 25.5346C26.436 26.1699 26.0031 26.7005 25.4725 27.1263C24.9489 27.5522 24.3904 27.8629 23.797 28.0583C23.2036 28.2468 22.4775 28.3585 21.6188 28.3935V29.8727C21.6188 30.1488 21.395 30.3727 21.1188 30.3727H20.5899C20.3137 30.3727 20.0899 30.1488 20.0899 29.8727V28.3935C19.0636 28.3027 18.2293 28.1386 17.5871 27.9013C16.9518 27.6569 16.4002 27.3218 15.9325 26.8959C15.4717 26.4701 15.1156 26.0128 14.8643 25.5241C14.7473 25.2933 14.6439 25.0405 14.5541 24.7658C14.3749 24.2175 14.7831 23.677 15.356 23.6104L17.5979 23.3496C18.1218 23.2886 18.5851 23.6516 18.7552 24.1508C18.8375 24.3926 18.9299 24.5886 19.0322 24.7387C19.2486 25.0459 19.6012 25.3182 20.0899 25.5555V21.8589C18.6796 21.4679 17.6778 21.1293 17.0844 20.8431C16.498 20.5568 15.9883 20.0996 15.5555 19.4712C15.1226 18.8359 14.9062 18.0645 14.9062 17.1569C14.9062 15.9142 15.3391 14.8775 16.2047 14.0467C17.0774 13.2089 18.3725 12.7447 20.0899 12.6539V12.1172C20.0899 11.841 20.3137 11.6172 20.5899 11.6172H21.1188C21.395 11.6172 21.6188 11.841 21.6188 12.1172V12.6539C23.1826 12.7517 24.3765 13.1217 25.2003 13.764C25.7621 14.1983 26.1914 14.7412 26.4883 15.3927ZM20.0899 15.3557C19.6431 15.5023 19.3324 15.6838 19.1579 15.9003C18.9833 16.1167 18.8961 16.3715 18.8961 16.6647C18.8961 16.9719 18.9833 17.2407 19.1579 17.4711C19.3394 17.6945 19.6501 17.883 20.0899 18.0366V15.3557ZM21.6188 25.6602C22.2122 25.5276 22.6451 25.3112 22.9173 25.011C23.1966 24.7038 23.3362 24.3617 23.3362 23.9847C23.3362 23.6566 23.2175 23.3564 22.9802 23.0841C22.7498 22.8049 22.296 22.5465 21.6188 22.3092V25.6602Z"
                    fill="#06ca4f"
                ></path>
            </symbol>
            <symbol id="svg-5-team" viewBox="0 0 37 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M22.7931 13.7124C24.5958 12.3055 25.7601 10.1258 25.7601 7.6769C25.7601 3.44405 22.2941 0 18.0323 0C13.7706 0 10.3044 3.44405 10.3044 7.6769C10.3044 10.1258 11.4689 12.3054 13.2714 13.7124C8.52057 15.5983 5.15278 20.2076 5.15278 25.5898V28.1481C5.15278 28.8557 5.72904 29.4279 6.44045 29.4279H29.6241C30.3357 29.4279 30.9117 28.8556 30.9117 28.1481V25.5898C30.9115 20.2056 27.5438 15.5982 22.7931 13.7124ZM12.4418 7.66667C12.4418 4.60356 14.9488 2.11316 18.0323 2.11316C21.1155 2.11316 23.6228 4.60331 23.6228 7.66667C23.6228 10.7291 21.1156 13.2202 18.0323 13.2202C14.949 13.2202 12.4418 10.7302 12.4418 7.66667ZM28.9748 27.3282H7.0886V25.9693C7.0886 19.9751 11.9978 15.0979 18.0332 15.0979C24.0664 15.0979 28.9747 19.9751 28.9747 25.9693L28.9748 27.3282ZM8.05063 14.8986L8.30219 14.8386C8.84225 14.6592 9.227 14.1881 9.227 13.6352C9.227 13.101 8.86744 12.6442 8.35731 12.4516C6.97822 11.8293 6.08864 10.4634 6.08864 8.95838C6.08864 7.25292 7.57066 5.81608 9.12178 5.31784C9.56077 3.99903 9.53051 3.37295 10.5 2.5C10.4341 2.56038 10.6074 2.5 10.5 2.5C6.94951 2.5 3.86403 5.43017 3.86403 8.95843C3.86403 10.5778 4.48528 12.0961 5.53942 13.2503C2.18665 14.9756 0 18.4265 0 22.3308V24.3097C0 25.0173 0.576266 25.5893 1.28767 25.5893H3.59537C3.59537 24.7126 3.68553 24.1506 3.85502 23.3236H2.22476V22.3307C2.22451 18.7942 4.61759 15.7595 8.05063 14.8986ZM30.5108 13.2503C31.5652 12.0959 32.1862 10.5779 32.1862 8.95848C32.1862 5.43021 29.2972 2.55926 25.746 2.55926C25.6389 2.55926 25.5396 2.58548 25.4354 2.59035C26.0678 3.48991 26.5436 4.54951 26.796 5.69303C26.5082 4.04532 27.048 5.28606 27.2324 6.63977C28.4787 5.81477 30.1752 7.25205 30.1752 8.95741C30.1752 10.4626 29.2844 11.8292 27.9065 12.4506C27.3964 12.6429 27.0366 13.0999 27.0366 13.634C27.0366 14.1871 27.4225 14.658 27.9615 14.8374L28.2131 14.8976C31.6472 15.7593 34.0391 18.7935 34.0391 22.3308V23.3238H32.1933C32.3635 24.1506 32.4527 24.7128 32.4527 25.5894H34.7604C35.4728 25.5894 36.048 25.0173 36.048 24.3099V22.3309C36.0502 18.4269 33.8638 14.9758 30.5108 13.2503Z"
                    fill="white"
                ></path>
                <path
                    d="M22.6404 13.7124C24.443 12.3055 25.6074 10.1258 25.6074 7.6769C25.6074 3.44405 22.1413 0 17.8795 0C13.6178 0 10.1517 3.44405 10.1517 7.6769C10.1517 10.1258 11.3161 12.3054 13.1187 13.7124C8.36779 15.5983 5 20.2076 5 25.5898V28.1481C5 28.8557 5.57627 29.4279 6.28767 29.4279H29.4713C30.1829 29.4279 30.759 28.8556 30.759 28.1481V25.5898C30.7587 20.2056 27.391 15.5982 22.6404 13.7124ZM12.289 7.66667C12.289 4.60356 14.796 2.11316 17.8795 2.11316C20.9628 2.11316 23.47 4.60331 23.47 7.66667C23.47 10.7291 20.9629 13.2202 17.8795 13.2202C14.7962 13.2202 12.289 10.7302 12.289 7.66667ZM28.822 27.3282H6.93582V25.9693C6.93582 19.9751 11.845 15.0979 17.8804 15.0979C23.9136 15.0979 28.8219 19.9751 28.8219 25.9693L28.822 27.3282Z"
                    fill="#06ca4f"
                ></path>
            </symbol>
            <symbol id="svg-5-tool-bg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" viewBox="0 0 54 54">
                <defs>
                    <lineargradient x1="0.20370370149612427" y1="0.07407408207654953" x2="0.40740740299224854" y2="0.29629626870155334" id="master_svg0_0_25286">
                        <stop offset="0%" stop-color="#06ca4f" stop-opacity="1"></stop>
                        <stop offset="100%" stop-color="#06ca4f" stop-opacity="0"></stop>
                    </lineargradient>
                </defs>
                <g>
                    <rect x="0" y="0" width="54" height="54" rx="18" fill="#FFFFFF" fill-opacity="0.11999999731779099"></rect>
                    <rect x="0.5" y="0.5" width="53" height="53" rx="17.5" stroke="url(#master_svg0_0_25286)" fill-opacity="0" fill="none" stroke-width="1"></rect>
                </g>
            </symbol>
            <symbol id="svg-5-withdraw" viewBox="0 0 39 39" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M21.0755 35.4573C20.4398 35.4576 19.8077 35.3608 19.2013 35.1702L11.5096 32.7544C11.4356 32.7296 11.3571 32.7212 11.2795 32.7296C11.2019 32.738 11.127 32.7631 11.06 32.8031L7.04087 35.2081C6.90355 35.2895 6.75128 35.3425 6.5931 35.3639C6.43492 35.3853 6.27405 35.3747 6.12004 35.3327C5.96936 35.2946 5.82768 35.2272 5.70313 35.1342C5.57857 35.0413 5.47359 34.9247 5.39421 34.791L2.68587 29.916C2.59623 29.7698 2.53951 29.6059 2.51966 29.4355C2.4998 29.2652 2.51729 29.0926 2.5709 28.9297C2.62452 28.7668 2.71297 28.6175 2.8301 28.4923C2.94724 28.367 3.09026 28.2688 3.24921 28.2044L11.06 24.9165C12.8653 24.1568 14.8885 24.095 16.7367 24.7431L23.5292 27.1156C23.7738 27.2051 23.9781 27.3798 24.1044 27.6076C24.2307 27.8354 24.2708 28.1011 24.2171 28.356L24.0655 29.0223C24.0016 29.3045 23.8695 29.5667 23.6806 29.7858C23.4917 30.0049 23.2518 30.1743 22.9821 30.279C22.3225 30.4958 21.6213 30.5552 20.9346 30.4523H20.8805L16.4821 30.0027C16.38 29.9924 16.281 29.9621 16.1906 29.9135C16.1003 29.8649 16.0204 29.799 15.9555 29.7195C15.8906 29.64 15.842 29.5486 15.8125 29.4503C15.783 29.3521 15.7731 29.249 15.7834 29.1469C15.7937 29.0448 15.824 28.9458 15.8726 28.8554C15.9212 28.765 15.9871 28.6851 16.0666 28.6203C16.146 28.5554 16.2375 28.5068 16.3358 28.4772C16.434 28.4477 16.5371 28.4378 16.6392 28.4481L21.0538 28.8977C21.5033 28.9704 21.9631 28.9464 22.4025 28.8273C22.4346 28.8156 22.4633 28.7961 22.486 28.7706C22.5087 28.7451 22.5247 28.7143 22.5325 28.681L22.5867 28.4481L16.2221 26.2165C14.7394 25.6952 13.1159 25.7434 11.6667 26.3519L4.21879 29.4773L6.55337 33.6427L10.2692 31.476C10.5251 31.3257 10.8096 31.2307 11.1044 31.1972C11.3992 31.1636 11.6978 31.1921 11.9809 31.281L19.6725 33.6969C20.8396 34.0602 22.1023 33.9553 23.1934 33.4044L34.4546 27.6248C34.0807 27.403 33.6599 27.2723 33.2261 27.2431C32.7923 27.214 32.3578 27.2872 31.9575 27.4569L25.7988 30.0244C25.6077 30.1041 25.3928 30.1047 25.2013 30.0259C25.0098 29.9472 24.8575 29.7957 24.7778 29.6046C24.698 29.4135 24.6975 29.1986 24.7762 29.0071C24.8549 28.8156 25.0065 28.6633 25.1975 28.5835L31.34 25.9998C32.0645 25.6979 32.8573 25.5984 33.6338 25.7119C34.4104 25.8254 35.1415 26.1477 35.7492 26.6444L36.1392 26.9585C36.2682 27.064 36.3688 27.1999 36.432 27.354C36.4952 27.5081 36.519 27.6755 36.5013 27.8411C36.4836 28.0067 36.4248 28.1653 36.3304 28.3025C36.2359 28.4397 36.1088 28.5512 35.9605 28.6269L23.8975 34.7748C23.0242 35.2226 22.0569 35.4565 21.0755 35.4573ZM21.0809 23.2915C19.114 23.2915 17.1913 22.7086 15.5555 21.6164C13.9198 20.5242 12.6444 18.9718 11.8905 17.1551C11.1366 15.3385 10.938 13.3392 11.3197 11.4098C11.7015 9.48031 12.6465 7.70727 14.0353 6.31461C15.4242 4.92194 17.1947 3.97213 19.1231 3.58514C21.0515 3.19815 23.0513 3.39134 24.87 4.14031C26.6887 4.88928 28.2446 6.16043 29.3412 7.79321C30.4378 9.42598 31.0259 11.3471 31.0313 13.314C31.0334 14.6225 30.7777 15.9187 30.2787 17.1285C29.7798 18.3382 29.0473 19.4377 28.1233 20.3643C27.1992 21.2909 26.1017 22.0263 24.8933 22.5285C23.6849 23.0308 22.3895 23.29 21.0809 23.2915ZM21.0809 4.94521C19.4205 4.94522 17.7974 5.43786 16.4172 6.36077C15.0369 7.28368 13.9615 8.59537 13.3271 10.1298C12.6927 11.6642 12.5278 13.3523 12.8533 14.9805C13.1788 16.6087 13.9801 18.1037 15.1557 19.2762C16.3312 20.4488 17.8283 21.2462 19.4573 21.5675C21.0863 21.8888 22.774 21.7195 24.3068 21.0811C25.8396 20.4428 27.1485 19.364 28.0678 17.9813C28.9871 16.5987 29.4756 14.9743 29.4713 13.314C29.4684 11.0896 28.5835 8.9571 27.0106 7.38421C25.4377 5.81132 23.3053 4.92641 21.0809 4.92355V4.94521Z"
                    fill="white"
                ></path>
                <path
                    d="M21.0809 20.6592C19.628 20.6603 18.2073 20.2304 16.9988 19.424C15.7902 18.6176 14.8479 17.4708 14.2911 16.1288C13.7344 14.7868 13.5881 13.3098 13.8709 11.8846C14.1537 10.4595 14.8529 9.15022 15.8799 8.12246C16.9069 7.0947 18.2156 6.39462 19.6405 6.11077C21.0655 5.82692 22.5426 5.97206 23.885 6.52783C25.2275 7.08359 26.3749 8.02502 27.1822 9.23301C27.9896 10.441 28.4205 11.8613 28.4205 13.3142C28.419 15.2609 27.6455 17.1275 26.2695 18.5044C24.8935 19.8814 23.0275 20.6564 21.0809 20.6592ZM21.0809 6.75466C19.7813 6.75037 18.5097 7.13189 17.4271 7.85089C16.3445 8.56988 15.4997 9.59401 14.9997 10.7935C14.4996 11.9931 14.3668 13.314 14.6181 14.5891C14.8694 15.8641 15.4935 17.0359 16.4113 17.956C17.3291 18.8761 18.4994 19.5031 19.7738 19.7575C21.0482 20.012 22.3695 19.8825 23.5703 19.3854C24.771 18.8883 25.7973 18.0461 26.5189 16.9653C27.2406 15.8845 27.6253 14.6138 27.6242 13.3142C27.6228 11.5773 26.9337 9.91155 25.7075 8.6813C24.4813 7.45105 22.8178 6.7564 21.0809 6.74924V6.75466Z"
                    fill="#06ca4f"
                ></path>
                <path
                    d="M22.4139 11.5591C22.28 11.3952 22.1092 11.2653 21.9155 11.18C21.715 11.0811 21.4945 11.0293 21.271 11.0283C21.1412 11.0285 21.0121 11.0449 20.8864 11.077C20.7618 11.1063 20.6429 11.1557 20.5343 11.2233C20.4285 11.2908 20.3396 11.3816 20.2743 11.4887C20.2036 11.6063 20.1679 11.7416 20.1714 11.8787C20.1676 12.0035 20.1975 12.127 20.258 12.2362C20.3191 12.3383 20.4024 12.4253 20.5018 12.4908C20.618 12.568 20.7436 12.6299 20.8755 12.675L21.3576 12.832C21.5562 12.897 21.7621 12.9711 21.9751 13.0541C22.1863 13.134 22.3851 13.2434 22.5655 13.3791C22.7457 13.5164 22.8951 13.6898 23.0043 13.8883C23.1221 14.1157 23.1816 14.3688 23.1776 14.625C23.1842 14.926 23.1212 15.2244 22.9935 15.497C22.8767 15.7367 22.7083 15.9475 22.5005 16.1145C22.2866 16.2829 22.0419 16.408 21.7801 16.4829C21.4988 16.5661 21.2069 16.6081 20.9135 16.6075C20.5076 16.6081 20.1053 16.5328 19.7272 16.3854C19.3599 16.2499 19.0335 16.0224 18.7793 15.7245L19.5701 14.9879C19.7289 15.2029 19.9395 15.3743 20.1822 15.4862C20.4142 15.6034 20.6698 15.6663 20.9297 15.6704C21.063 15.6699 21.1957 15.6535 21.3251 15.6216C21.4554 15.5912 21.5786 15.536 21.688 15.4591C21.7963 15.3838 21.8868 15.2858 21.9535 15.172C22.026 15.0414 22.0615 14.8935 22.0564 14.7441C22.0612 14.6002 22.0215 14.4583 21.9426 14.3379C21.8601 14.2239 21.755 14.128 21.6339 14.0562C21.4907 13.9703 21.338 13.9012 21.1789 13.8504L20.6372 13.6662C20.453 13.6055 20.2722 13.535 20.0955 13.455C19.9111 13.3748 19.7409 13.265 19.5918 13.13C19.4394 12.9913 19.316 12.8238 19.2289 12.637C19.1168 12.413 19.0558 12.167 19.0501 11.9166C19.0422 11.6316 19.1095 11.3495 19.2451 11.0987C19.3833 10.8802 19.5683 10.6952 19.7868 10.557C20.0073 10.4036 20.2529 10.2899 20.5126 10.2212C20.783 10.149 21.0616 10.1126 21.3414 10.1129C21.6681 10.1154 21.992 10.1722 22.3001 10.2808C22.6138 10.3853 22.9018 10.5552 23.1451 10.7791L22.4139 11.5591ZM21.0814 9.34912V10.4866V9.34912Z"
                    fill="#06ca4f"
                ></path>
                <path d="M20.5078 9.34912H21.6507V10.4866H20.5078V9.34912ZM21.082 16.3691V17.5066V16.3691Z" fill="#06ca4f"></path>
                <path d="M20.5078 16.3691H21.6507V17.5066H20.5078V16.3691Z" fill="white"></path>
            </symbol>
        </svg>
        <div id="app" data-v-app="" class="a-t-5 no-1">
            <div class="van-config-provider">
                <div data-v-a652ce47="" class="box-border min-h-full w-full layout-tab-bar px-$mg pt-45px">
                                        <div data-v-a652ce47="" class="nav-bar-wrap" style="--71594674: 0;margin-top: -25px;">
                        <div class="nav-bar">
                             <div data-v-fcd9c982="" class="navbar-wrap">
                                <div data-v-fcd9c982="" class="w-full px-$mg flex justify-between items-center">
                                    <div data-v-fcd9c982="" class="app-info-wrap">
                                        <div data-v-fcd9c982="" class="w-88px h-auto max-h-88px"><img data-v-fcd9c982="" class="w-full h-full" src="{{ asset(getImage(getFilePath('logoIcon') . '/logo_2.png')) }}" /></div>

                                        <div data-v-fcd9c982="" class="font-bold text-bas ml-4px"></div>
                                    </div>
                                    <div data-v-fcd9c982="" class="tools-wrap">
                                        <div data-v-fcd9c982="" class="w-30px h-30px bg-#fff bg-opacity-20 rounded-full flex items-center justify-center">
                                            <svg data-v-fcd9c982="" class="svg-icon 5-lang w-20px h-20px w-20px h-20px" aria-hidden="true">
                                                <use xlink:href="#svg-5-lang"></use>
                                            </svg>
                                        </div>
                                        <div data-v-fcd9c982="" class="ml-16px w-30px h-30px bg-#fff bg-opacity-20 rounded-full flex items-center justify-center">
                                            <svg data-v-fcd9c982="" class="svg-icon 5-message w-16px h-16px w-16px h-16px" aria-hidden="true">
                                                <use xlink:href="#svg-5-message"></use>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                           
                        </div>
                    </div>
                    <!--<div data-v-f09a6d59="" data-v-a652ce47="" class="navigation">-->
                    <!--    <div data-v-f09a6d59="" class="navigation-content">-->
                    <!--        <div data-v-f09a6d59="" class="h-full flex cursor-pointer items-center justify-between">-->
                    <!--            <div data-v-f09a6d59="" class="icon i-material-symbols-arrow-back-ios-new-rounded"></div>-->
                    <!--            <div data-v-f09a6d59=""></div>-->
                    <!--            <div data-v-f09a6d59="" class="opacity-0">h</div>-->
                    <!--        </div>-->
                    <!--    </div>-->
                    <!--</div>-->
                                        
                        
    <div data-v-3148964a="" data-v-a652ce47="" class="invest-wrap">
    <div data-v-3148964a="" class="top-wrap sticky top-0px z-2 w-full flex flex-col rounded-b-xl py-2 space-y-3">
        <div data-v-3148964a="" class="flex-center py-2 text-xl font-bold">Invest</div>
        <div data-v-3148964a="" class="mr-24 w-full flex items-center justify-between py-2">
            <div data-v-3148964a="" class=":uno: base-user-tab flex items-center justify-center w-full">
                <div class=":uno: tab-item h-full flex cursor-pointer items-center justify-center active">
                    <div class="text-center">Smart investment</div>
                </div>
                <a href="{{route ('user.invest.log')}}" class=":uno: tab-item h-full flex cursor-pointer items-center justify-center">
                    <div class="text-center">Invest Record</div>
                </a>
            </div>
        </div>
    </div>
    <div data-v-3148964a="" class="mt-10px">
        
       
        
        
                <div data-v-3148964a="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text mb-10px cursor-pointer">
            
     
            <button type="submit" data-v-3148964a="" class="flex justify-start"  style="background: none;align-items: center;text-align: left;">
                <img data-v-3148964a="" src="/assets/images/logoIcon/favicon.png" class="h-120px w-120px shrink-0 rounded"/>
                <div data-v-3148964a="" class="ml-10px">
              
                 
                    <div data-v-3148964a="" class="my-5px text-$text-gray">All Gains: {{ $totalInvest }}</div>
                    <div data-v-3148964a="" class="my-5px text-$text-gray">Total Gain: {{ $general->cur_sym }}{{ showAmount($interests) }}</div>
                </div>
                
                 <div onclick="location.href='/user/invest/statistics';" class=":uno: tab-item h-full flex cursor-pointer items-center justify-center"><div class="text-center">Completed</div></div></div><div data-v-cbbf7adf="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text"><div data-v-cbbf7adf="" class="task-list">
      
        @forelse($invests as $invest)
        @php
            if ($invest->last_time) {
                $start = $invest->last_time;
            } else {
                $start = $invest->created_at;
            }
        @endphp
        
         @if ($invest->status == 1)
      <div data-v-cbbf7adf="" class="mission-card"><div data-v-cbbf7adf="" class="card-inner">
        <div data-v-cbbf7adf="" class="product-show">
          <center>  <img data-v-cbbf7adf="" class="product-img" src="/assets/images/logoIcon/favicon.png"></center>
    </div>
    <div data-v-cbbf7adf="" class="product-info-wrap"><div data-v-cbbf7adf="" class="product-info">
    <center>   <div data-v-cbbf7adf="" class="product-name">{{ __($invest->plan->name) }}</div>
        <div data-v-cbbf7adf="" class="main-price-info"><div data-v-cbbf7adf="" class="product-price">
            <div data-v-cbbf7adf="" style="color:white" class="label">Total Gain</div>
            <div data-v-cbbf7adf="" class="price c-#fff">{{ showAmount($invest->paid) }} {{ __($general->cur_text) }}</div>   </center>
        </div>
    </div>
</div>
</div>
</div>
</div>
      @endif
      
      @empty
      
      <div data-v-cbbf7adf="" class=":uno: container-card relative rd-$card-radius p-$mg c-$btn-text scale-x"><div data-v-cbbf7adf="" class="task-list"><!----><div data-v-e7ebc72f="" class="base-list a-t-2"><div data-v-e7ebc72f="" class="base-list-nodata">No data</div><!----><!----><!----></div></div></div>
      
      @endforelse
              
                                 </form>
                                 
        </div>
        
   
        
        
            </div>
    <!---->
</div>


